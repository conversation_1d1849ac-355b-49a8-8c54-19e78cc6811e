"""
Data Visualization Lab - Activities 3-7
Working with Sales Data CSV file

This script implements:
- Activity 3: Exploring Sample Data
- Activity 4: Creating Basic Visualizations
- Activity 5: Customizing Visualizations
- Activity 6: Presenting Insights
- Activity 7: Sharing and Exporting
"""

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Set style for better looking plots
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

class SalesDataAnalyzer:
    def __init__(self, csv_file):
        """Initialize the analyzer with sales data"""
        self.csv_file = csv_file
        self.df = None
        self.load_data()

    def load_data(self):
        """Activity 3: Load the data into the visualization tool"""
        try:
            self.df = pd.read_csv(self.csv_file)
            print("✅ Data loaded successfully!")
            print(f"Dataset shape: {self.df.shape}")
            print("\nFirst few rows:")
            print(self.df.head())
        except Exception as e:
            print(f"❌ Error loading data: {e}")
            return False
        return True

    def explore_data(self):
        """Activity 3: Practice basic data exploration"""
        print("\n" + "="*50)
        print("📊 DATA EXPLORATION")
        print("="*50)

        # Basic info about the dataset
        print("\n1. Dataset Information:")
        print(f"   - Total records: {len(self.df)}")
        print(f"   - Columns: {list(self.df.columns)}")
        print(f"   - Data types:\n{self.df.dtypes}")

        # Convert Date column to datetime
        self.df['Date'] = pd.to_datetime(self.df['Date'])

        # Basic statistics
        print("\n2. Basic Statistics:")
        print(self.df.describe())

        # Viewing data - show unique products
        print("\n3. Unique Products:")
        unique_products = self.df['Product'].unique()
        print(f"   Products: {unique_products}")
        print(f"   Number of unique products: {len(unique_products)}")

        # Filtering example - show high value sales (>180)
        print("\n4. Filtering - High Value Sales (Amount > 180):")
        high_value_sales = self.df[self.df['Amount'] > 180]
        print(f"   Number of high value sales: {len(high_value_sales)}")
        print(high_value_sales.head())

        # Sorting example - top 10 sales by amount
        print("\n5. Sorting - Top 10 Sales by Amount:")
        top_sales = self.df.nlargest(10, 'Amount')
        print(top_sales)

        # Group by product
        print("\n6. Sales Summary by Product:")
        product_summary = self.df.groupby('Product').agg({
            'Amount': ['count', 'sum', 'mean', 'min', 'max']
        }).round(2)
        product_summary.columns = ['Count', 'Total_Sales', 'Avg_Sales', 'Min_Sales', 'Max_Sales']
        print(product_summary)

        return product_summary

    def create_bar_chart(self):
        """Activity 4: Create bar charts"""
        print("\n" + "="*50)
        print("📊 CREATING BAR CHARTS")
        print("="*50)

        # Calculate total sales by product
        product_sales = self.df.groupby('Product')['Amount'].sum().sort_values(ascending=False)

        # Create figure with subplots
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

        # Bar chart 1: Total Sales by Product
        bars1 = ax1.bar(product_sales.index, product_sales.values,
                       color=['#FF6B6B', '#4ECDC4', '#45B7D1'], alpha=0.8)
        ax1.set_title('Total Sales by Product', fontsize=14, fontweight='bold', pad=20)
        ax1.set_xlabel('Product', fontsize=12)
        ax1.set_ylabel('Total Sales Amount', fontsize=12)
        ax1.grid(axis='y', alpha=0.3)

        # Add value labels on bars
        for bar in bars1:
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 50,
                    f'${height:,.0f}', ha='center', va='bottom', fontweight='bold')

        # Bar chart 2: Average Sales by Product
        avg_sales = self.df.groupby('Product')['Amount'].mean().sort_values(ascending=False)
        bars2 = ax2.bar(avg_sales.index, avg_sales.values,
                       color=['#96CEB4', '#FFEAA7', '#DDA0DD'], alpha=0.8)
        ax2.set_title('Average Sales by Product', fontsize=14, fontweight='bold', pad=20)
        ax2.set_xlabel('Product', fontsize=12)
        ax2.set_ylabel('Average Sales Amount', fontsize=12)
        ax2.grid(axis='y', alpha=0.3)

        # Add value labels on bars
        for bar in bars2:
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 2,
                    f'${height:.1f}', ha='center', va='bottom', fontweight='bold')

        plt.tight_layout()
        plt.savefig('bar_charts.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("✅ Bar charts created and saved as 'bar_charts.png'")
        return product_sales, avg_sales

    def create_line_chart(self):
        """Activity 4: Create line charts"""
        print("\n" + "="*50)
        print("📈 CREATING LINE CHARTS")
        print("="*50)

        # Prepare data for time series analysis
        self.df['Month'] = self.df['Date'].dt.to_period('M')
        monthly_sales = self.df.groupby(['Month', 'Product'])['Amount'].sum().unstack(fill_value=0)
        monthly_total = self.df.groupby('Month')['Amount'].sum()

        # Create figure with subplots
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))

        # Line chart 1: Monthly sales by product
        for product in monthly_sales.columns:
            ax1.plot(monthly_sales.index.astype(str), monthly_sales[product],
                    marker='o', linewidth=2.5, markersize=6, label=product)

        ax1.set_title('Monthly Sales Trends by Product', fontsize=14, fontweight='bold', pad=20)
        ax1.set_xlabel('Month', fontsize=12)
        ax1.set_ylabel('Sales Amount', fontsize=12)
        ax1.legend(title='Product', title_fontsize=12, fontsize=10)
        ax1.grid(True, alpha=0.3)
        ax1.tick_params(axis='x', rotation=45)

        # Line chart 2: Total monthly sales trend
        ax2.plot(monthly_total.index.astype(str), monthly_total.values,
                marker='s', linewidth=3, markersize=8, color='#E74C3C', alpha=0.8)
        ax2.fill_between(monthly_total.index.astype(str), monthly_total.values,
                        alpha=0.3, color='#E74C3C')
        ax2.set_title('Total Monthly Sales Trend', fontsize=14, fontweight='bold', pad=20)
        ax2.set_xlabel('Month', fontsize=12)
        ax2.set_ylabel('Total Sales Amount', fontsize=12)
        ax2.grid(True, alpha=0.3)
        ax2.tick_params(axis='x', rotation=45)

        # Add annotations for highest and lowest points
        max_idx = monthly_total.idxmax()
        min_idx = monthly_total.idxmin()
        ax2.annotate(f'Peak: ${monthly_total.max():,.0f}',
                    xy=(str(max_idx), monthly_total.max()),
                    xytext=(10, 10), textcoords='offset points',
                    bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7),
                    arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))

        plt.tight_layout()
        plt.savefig('line_charts.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("✅ Line charts created and saved as 'line_charts.png'")
        return monthly_sales, monthly_total

    def create_scatter_plot(self):
        """Activity 4: Create scatter plots"""
        print("\n" + "="*50)
        print("🔍 CREATING SCATTER PLOTS")
        print("="*50)

        # Add day of year for scatter plot analysis
        self.df['DayOfYear'] = self.df['Date'].dt.dayofyear

        # Create figure with subplots
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))

        # Scatter plot 1: Sales Amount vs Day of Year (colored by product)
        products = self.df['Product'].unique()
        colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']

        for i, product in enumerate(products):
            product_data = self.df[self.df['Product'] == product]
            ax1.scatter(product_data['DayOfYear'], product_data['Amount'],
                       c=colors[i], label=product, alpha=0.7, s=50)

        ax1.set_title('Sales Amount vs Day of Year by Product', fontsize=14, fontweight='bold', pad=20)
        ax1.set_xlabel('Day of Year', fontsize=12)
        ax1.set_ylabel('Sales Amount', fontsize=12)
        ax1.legend(title='Product', title_fontsize=12)
        ax1.grid(True, alpha=0.3)

        # Add trend line
        z = np.polyfit(self.df['DayOfYear'], self.df['Amount'], 1)
        p = np.poly1d(z)
        ax1.plot(self.df['DayOfYear'], p(self.df['DayOfYear']), "r--", alpha=0.8, linewidth=2)

        # Scatter plot 2: Sales distribution with size based on amount
        # Create a bubble chart showing relationship between date and amount
        self.df['DateNum'] = (self.df['Date'] - self.df['Date'].min()).dt.days

        for i, product in enumerate(products):
            product_data = self.df[self.df['Product'] == product]
            ax2.scatter(product_data['DateNum'], product_data['Amount'],
                       c=colors[i], label=product, alpha=0.6,
                       s=product_data['Amount']/2)  # Size based on amount

        ax2.set_title('Sales Bubble Chart (Size = Amount)', fontsize=14, fontweight='bold', pad=20)
        ax2.set_xlabel('Days from Start', fontsize=12)
        ax2.set_ylabel('Sales Amount', fontsize=12)
        ax2.legend(title='Product', title_fontsize=12)
        ax2.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('scatter_plots.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("✅ Scatter plots created and saved as 'scatter_plots.png'")

        # Calculate correlation
        correlation = self.df['DayOfYear'].corr(self.df['Amount'])
        print(f"📊 Correlation between Day of Year and Sales Amount: {correlation:.3f}")

        return correlation

    def create_advanced_visualizations(self):
        """Activity 5: Customizing Visualizations with advanced features"""
        print("\n" + "="*50)
        print("🎨 ADVANCED CUSTOMIZED VISUALIZATIONS")
        print("="*50)

        # Create a comprehensive dashboard
        fig = plt.figure(figsize=(20, 12))

        # Define a custom color palette
        custom_colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD']

        # 1. Heatmap of sales by product and month
        ax1 = plt.subplot(2, 3, 1)
        monthly_product_sales = self.df.groupby([self.df['Date'].dt.month, 'Product'])['Amount'].sum().unstack(fill_value=0)
        sns.heatmap(monthly_product_sales, annot=True, fmt='.0f', cmap='YlOrRd',
                   cbar_kws={'label': 'Sales Amount'}, ax=ax1)
        ax1.set_title('Sales Heatmap by Month and Product', fontsize=12, fontweight='bold')
        ax1.set_xlabel('Product')
        ax1.set_ylabel('Month')

        # 2. Box plot of sales distribution by product
        ax2 = plt.subplot(2, 3, 2)
        box_plot = ax2.boxplot([self.df[self.df['Product'] == product]['Amount'].values
                               for product in self.df['Product'].unique()],
                              labels=self.df['Product'].unique(),
                              patch_artist=True)

        # Customize box plot colors
        for patch, color in zip(box_plot['boxes'], custom_colors):
            patch.set_facecolor(color)
            patch.set_alpha(0.7)

        ax2.set_title('Sales Distribution by Product', fontsize=12, fontweight='bold')
        ax2.set_xlabel('Product')
        ax2.set_ylabel('Sales Amount')
        ax2.grid(axis='y', alpha=0.3)

        # 3. Pie chart of total sales by product
        ax3 = plt.subplot(2, 3, 3)
        product_totals = self.df.groupby('Product')['Amount'].sum()
        wedges, texts, autotexts = ax3.pie(product_totals.values, labels=product_totals.index,
                                          autopct='%1.1f%%', startangle=90, colors=custom_colors[:3])
        ax3.set_title('Sales Share by Product', fontsize=12, fontweight='bold')

        # Enhance pie chart text
        for autotext in autotexts:
            autotext.set_color('white')
            autotext.set_fontweight('bold')

        # 4. Histogram of sales amounts
        ax4 = plt.subplot(2, 3, 4)
        ax4.hist(self.df['Amount'], bins=20, color='#45B7D1', alpha=0.7, edgecolor='black')
        ax4.axvline(self.df['Amount'].mean(), color='red', linestyle='--', linewidth=2,
                   label=f'Mean: ${self.df["Amount"].mean():.1f}')
        ax4.axvline(self.df['Amount'].median(), color='green', linestyle='--', linewidth=2,
                   label=f'Median: ${self.df["Amount"].median():.1f}')
        ax4.set_title('Distribution of Sales Amounts', fontsize=12, fontweight='bold')
        ax4.set_xlabel('Sales Amount')
        ax4.set_ylabel('Frequency')
        ax4.legend()
        ax4.grid(axis='y', alpha=0.3)

        # 5. Weekly sales trend
        ax5 = plt.subplot(2, 3, 5)
        self.df['Week'] = self.df['Date'].dt.isocalendar().week
        weekly_sales = self.df.groupby('Week')['Amount'].sum()
        ax5.plot(weekly_sales.index, weekly_sales.values, marker='o', linewidth=2,
                markersize=4, color='#E74C3C')
        ax5.fill_between(weekly_sales.index, weekly_sales.values, alpha=0.3, color='#E74C3C')
        ax5.set_title('Weekly Sales Trend', fontsize=12, fontweight='bold')
        ax5.set_xlabel('Week Number')
        ax5.set_ylabel('Total Sales')
        ax5.grid(True, alpha=0.3)

        # 6. Product performance comparison
        ax6 = plt.subplot(2, 3, 6)
        product_stats = self.df.groupby('Product')['Amount'].agg(['mean', 'std']).reset_index()
        x_pos = range(len(product_stats))
        bars = ax6.bar(x_pos, product_stats['mean'], yerr=product_stats['std'],
                      capsize=5, color=custom_colors[:3], alpha=0.8,
                      error_kw={'elinewidth': 2, 'capthick': 2})
        ax6.set_title('Average Sales with Standard Deviation', fontsize=12, fontweight='bold')
        ax6.set_xlabel('Product')
        ax6.set_ylabel('Average Sales Amount')
        ax6.set_xticks(x_pos)
        ax6.set_xticklabels(product_stats['Product'])
        ax6.grid(axis='y', alpha=0.3)

        plt.tight_layout()
        plt.savefig('advanced_dashboard.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("✅ Advanced dashboard created and saved as 'advanced_dashboard.png'")
        return product_stats

    def present_insights(self):
        """Activity 6: Presenting Insights with meaningful information"""
        print("\n" + "="*50)
        print("💡 BUSINESS INSIGHTS & ANALYSIS")
        print("="*50)

        # Calculate key metrics
        total_sales = self.df['Amount'].sum()
        avg_daily_sales = self.df['Amount'].mean()
        best_product = self.df.groupby('Product')['Amount'].sum().idxmax()
        worst_product = self.df.groupby('Product')['Amount'].sum().idxmin()

        # Date range analysis
        date_range = (self.df['Date'].max() - self.df['Date'].min()).days

        # Product performance
        product_performance = self.df.groupby('Product').agg({
            'Amount': ['sum', 'mean', 'count', 'std']
        }).round(2)
        product_performance.columns = ['Total_Sales', 'Avg_Sales', 'Transactions', 'Std_Dev']

        # Create insights visualization
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

        # Insight 1: Key Performance Indicators
        ax1.axis('off')
        kpi_text = f"""
        📊 KEY PERFORMANCE INDICATORS

        💰 Total Sales Revenue: ${total_sales:,.2f}
        📈 Average Daily Sales: ${avg_daily_sales:.2f}
        📅 Analysis Period: {date_range} days
        🏆 Best Performing Product: {best_product}
        📉 Lowest Performing Product: {worst_product}

        🎯 INSIGHTS:
        • {best_product} generates the highest revenue
        • Sales show consistent patterns across products
        • Average transaction value varies by product type
        """
        ax1.text(0.05, 0.95, kpi_text, transform=ax1.transAxes, fontsize=11,
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))

        # Insight 2: Monthly trend with annotations
        monthly_trend = self.df.groupby(self.df['Date'].dt.month)['Amount'].sum()
        ax2.plot(monthly_trend.index, monthly_trend.values, marker='o', linewidth=3, markersize=8)
        ax2.fill_between(monthly_trend.index, monthly_trend.values, alpha=0.3)

        # Add annotations for key insights
        peak_month = monthly_trend.idxmax()
        low_month = monthly_trend.idxmin()

        ax2.annotate(f'Peak Month: {peak_month}\n${monthly_trend.max():,.0f}',
                    xy=(peak_month, monthly_trend.max()),
                    xytext=(peak_month+0.5, monthly_trend.max()+500),
                    arrowprops=dict(arrowstyle='->', color='red', lw=2),
                    bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.8),
                    fontsize=10, fontweight='bold')

        ax2.annotate(f'Low Month: {low_month}\n${monthly_trend.min():,.0f}',
                    xy=(low_month, monthly_trend.min()),
                    xytext=(low_month+0.5, monthly_trend.min()-500),
                    arrowprops=dict(arrowstyle='->', color='blue', lw=2),
                    bbox=dict(boxstyle='round,pad=0.3', facecolor='lightcoral', alpha=0.8),
                    fontsize=10, fontweight='bold')

        ax2.set_title('Monthly Sales Trend with Key Insights', fontsize=14, fontweight='bold')
        ax2.set_xlabel('Month')
        ax2.set_ylabel('Total Sales')
        ax2.grid(True, alpha=0.3)

        # Insight 3: Product comparison with performance indicators
        product_totals = self.df.groupby('Product')['Amount'].sum().sort_values(ascending=True)
        colors = ['#FF6B6B' if x == product_totals.max() else '#4ECDC4' if x == product_totals.min()
                 else '#45B7D1' for x in product_totals.values]

        bars = ax3.barh(product_totals.index, product_totals.values, color=colors, alpha=0.8)
        ax3.set_title('Product Performance Ranking', fontsize=14, fontweight='bold')
        ax3.set_xlabel('Total Sales Revenue')

        # Add value labels and performance indicators
        for i, (bar, value) in enumerate(zip(bars, product_totals.values)):
            ax3.text(value + 200, bar.get_y() + bar.get_height()/2,
                    f'${value:,.0f}', va='center', fontweight='bold')

            # Add performance indicator
            if value == product_totals.max():
                ax3.text(value/2, bar.get_y() + bar.get_height()/2,
                        '🏆 TOP PERFORMER', va='center', ha='center',
                        fontweight='bold', color='white', fontsize=10)
            elif value == product_totals.min():
                ax3.text(value/2, bar.get_y() + bar.get_height()/2,
                        '📈 GROWTH OPPORTUNITY', va='center', ha='center',
                        fontweight='bold', color='white', fontsize=9)

        # Insight 4: Sales volatility analysis
        daily_sales = self.df.groupby('Date')['Amount'].sum()
        rolling_avg = daily_sales.rolling(window=7).mean()

        ax4.plot(daily_sales.index, daily_sales.values, alpha=0.5, color='lightgray', label='Daily Sales')
        ax4.plot(rolling_avg.index, rolling_avg.values, color='red', linewidth=2, label='7-day Moving Average')

        # Calculate and show volatility
        volatility = daily_sales.std()
        ax4.axhline(y=daily_sales.mean(), color='green', linestyle='--', alpha=0.8,
                   label=f'Average: ${daily_sales.mean():.0f}')
        ax4.axhline(y=daily_sales.mean() + volatility, color='orange', linestyle=':', alpha=0.8,
                   label=f'+1 Std Dev: ${daily_sales.mean() + volatility:.0f}')
        ax4.axhline(y=daily_sales.mean() - volatility, color='orange', linestyle=':', alpha=0.8,
                   label=f'-1 Std Dev: ${daily_sales.mean() - volatility:.0f}')

        ax4.set_title('Sales Volatility Analysis', fontsize=14, fontweight='bold')
        ax4.set_xlabel('Date')
        ax4.set_ylabel('Daily Sales')
        ax4.legend(fontsize=9)
        ax4.tick_params(axis='x', rotation=45)

        plt.tight_layout()
        plt.savefig('business_insights.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("✅ Business insights visualization created and saved as 'business_insights.png'")

        # Print detailed insights
        print(f"\n📈 DETAILED ANALYSIS:")
        print(f"   • Sales Volatility (Std Dev): ${volatility:.2f}")
        print(f"   • Best Month Performance: Month {peak_month} (${monthly_trend.max():,.0f})")
        print(f"   • Improvement Opportunity: Month {low_month} (${monthly_trend.min():,.0f})")
        print(f"   • Revenue Gap: ${monthly_trend.max() - monthly_trend.min():,.0f}")

        return {
            'total_sales': total_sales,
            'volatility': volatility,
            'best_month': peak_month,
            'product_performance': product_performance
        }

    def export_and_share(self):
        """Activity 7: Sharing and Exporting visualizations"""
        print("\n" + "="*50)
        print("📤 EXPORTING & SHARING VISUALIZATIONS")
        print("="*50)

        # Create a summary report
        summary_data = {
            'Metric': ['Total Sales', 'Average Daily Sales', 'Number of Products',
                      'Total Transactions', 'Analysis Period (days)', 'Best Product', 'Sales Volatility'],
            'Value': [f"${self.df['Amount'].sum():,.2f}",
                     f"${self.df['Amount'].mean():.2f}",
                     f"{self.df['Product'].nunique()}",
                     f"{len(self.df)}",
                     f"{(self.df['Date'].max() - self.df['Date'].min()).days}",
                     f"{self.df.groupby('Product')['Amount'].sum().idxmax()}",
                     f"${self.df['Amount'].std():.2f}"]
        }

        summary_df = pd.DataFrame(summary_data)

        # Export summary to CSV
        summary_df.to_csv('sales_analysis_summary.csv', index=False)
        print("✅ Summary report exported to 'sales_analysis_summary.csv'")

        # Export detailed data analysis
        detailed_analysis = self.df.groupby('Product').agg({
            'Amount': ['count', 'sum', 'mean', 'min', 'max', 'std']
        }).round(2)
        detailed_analysis.columns = ['Transactions', 'Total_Sales', 'Avg_Sales',
                                   'Min_Sales', 'Max_Sales', 'Std_Dev']
        detailed_analysis.to_csv('detailed_product_analysis.csv')
        print("✅ Detailed analysis exported to 'detailed_product_analysis.csv'")

        # Create a final presentation-ready visualization
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 10))
        fig.suptitle('Sales Data Analysis - Executive Summary', fontsize=16, fontweight='bold', y=0.98)

        # 1. Total sales by product (clean bar chart)
        product_sales = self.df.groupby('Product')['Amount'].sum().sort_values(ascending=False)
        bars1 = ax1.bar(product_sales.index, product_sales.values,
                       color=['#2E86AB', '#A23B72', '#F18F01'], alpha=0.8)
        ax1.set_title('Total Revenue by Product', fontweight='bold')
        ax1.set_ylabel('Revenue ($)')
        for bar in bars1:
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 100,
                    f'${height:,.0f}', ha='center', va='bottom', fontweight='bold')

        # 2. Monthly trend
        monthly_sales = self.df.groupby(self.df['Date'].dt.month)['Amount'].sum()
        ax2.plot(monthly_sales.index, monthly_sales.values, marker='o',
                linewidth=3, markersize=8, color='#2E86AB')
        ax2.fill_between(monthly_sales.index, monthly_sales.values, alpha=0.3, color='#2E86AB')
        ax2.set_title('Monthly Sales Trend', fontweight='bold')
        ax2.set_xlabel('Month')
        ax2.set_ylabel('Total Sales ($)')
        ax2.grid(True, alpha=0.3)

        # 3. Sales distribution
        ax3.hist(self.df['Amount'], bins=15, color='#A23B72', alpha=0.7, edgecolor='black')
        ax3.axvline(self.df['Amount'].mean(), color='red', linestyle='--', linewidth=2,
                   label=f'Mean: ${self.df["Amount"].mean():.0f}')
        ax3.set_title('Sales Amount Distribution', fontweight='bold')
        ax3.set_xlabel('Sales Amount ($)')
        ax3.set_ylabel('Frequency')
        ax3.legend()

        # 4. Key metrics summary
        ax4.axis('off')
        metrics_text = f"""
        📊 KEY METRICS SUMMARY

        💰 Total Revenue: ${self.df['Amount'].sum():,.0f}
        📈 Average Sale: ${self.df['Amount'].mean():.0f}
        🏆 Top Product: {self.df.groupby('Product')['Amount'].sum().idxmax()}
        📅 Period: {self.df['Date'].min().strftime('%Y-%m-%d')} to {self.df['Date'].max().strftime('%Y-%m-%d')}
        📊 Total Transactions: {len(self.df):,}

        🎯 RECOMMENDATIONS:
        • Focus marketing on top-performing products
        • Investigate seasonal trends for planning
        • Optimize inventory based on sales patterns
        """
        ax4.text(0.05, 0.95, metrics_text, transform=ax4.transAxes, fontsize=11,
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8))

        plt.tight_layout()
        plt.savefig('executive_summary.png', dpi=300, bbox_inches='tight')
        plt.savefig('executive_summary.pdf', dpi=300, bbox_inches='tight')  # PDF for presentations
        plt.show()

        print("✅ Executive summary created and saved as:")
        print("   - executive_summary.png (for web/email)")
        print("   - executive_summary.pdf (for presentations)")

        # Create a list of all generated files
        generated_files = [
            'bar_charts.png',
            'line_charts.png',
            'scatter_plots.png',
            'advanced_dashboard.png',
            'business_insights.png',
            'executive_summary.png',
            'executive_summary.pdf',
            'sales_analysis_summary.csv',
            'detailed_product_analysis.csv'
        ]

        print(f"\n📁 All generated files ({len(generated_files)} total):")
        for i, file in enumerate(generated_files, 1):
            print(f"   {i}. {file}")

        return generated_files, summary_df, detailed_analysis

    def run_complete_analysis(self):
        """Run all activities in sequence"""
        print("🚀 STARTING COMPLETE SALES DATA ANALYSIS")
        print("="*60)

        # Activity 3: Data Exploration
        product_summary = self.explore_data()

        # Activity 4: Basic Visualizations
        product_sales, avg_sales = self.create_bar_chart()
        monthly_sales, monthly_total = self.create_line_chart()
        correlation = self.create_scatter_plot()

        # Activity 5: Advanced Customizations
        product_stats = self.create_advanced_visualizations()

        # Activity 6: Business Insights
        insights = self.present_insights()

        # Activity 7: Export and Share
        files, summary, detailed = self.export_and_share()

        print("\n" + "="*60)
        print("🎉 ANALYSIS COMPLETE!")
        print("="*60)
        print("All activities have been successfully completed:")
        print("✅ Activity 3: Data exploration and filtering")
        print("✅ Activity 4: Bar charts, line charts, and scatter plots")
        print("✅ Activity 5: Customized visualizations with colors and labels")
        print("✅ Activity 6: Business insights with meaningful annotations")
        print("✅ Activity 7: Exported visualizations and reports")

        return {
            'product_summary': product_summary,
            'insights': insights,
            'generated_files': files,
            'correlation': correlation
        }


# Main execution
if __name__ == "__main__":
    # Initialize the analyzer
    analyzer = SalesDataAnalyzer('sales_data.csv')

    # Run complete analysis
    results = analyzer.run_complete_analysis()

    print(f"\n🎯 Analysis completed successfully!")
    print(f"📊 Generated {len(results['generated_files'])} visualization files")
    print(f"📈 Sales correlation with time: {results['correlation']:.3f}")
    print(f"💰 Total revenue analyzed: ${results['insights']['total_sales']:,.2f}")