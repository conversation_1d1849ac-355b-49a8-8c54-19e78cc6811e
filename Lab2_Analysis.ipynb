# Import required libraries
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Set style for better looking plots
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

# Set figure size defaults
plt.rcParams['figure.figsize'] = (12, 8)
plt.rcParams['font.size'] = 10

print("✅ Libraries imported successfully!")

# Activity 3: Load the data into the visualization tool
df = pd.read_csv('sales_data.csv')

print("✅ Data loaded successfully!")
print(f"Dataset shape: {df.shape}")
print("\nFirst few rows:")
df.head()

# Convert Date column to datetime for better analysis
df['Date'] = pd.to_datetime(df['Date'])

# Basic dataset information
print("📊 DATASET OVERVIEW")
print("=" * 40)
print(f"Total records: {len(df)}")
print(f"Columns: {list(df.columns)}")
print(f"Date range: {df['Date'].min()} to {df['Date'].max()}")
print(f"Analysis period: {(df['Date'].max() - df['Date'].min()).days} days")

# Data types
print("\n📋 DATA TYPES:")
print(df.dtypes)

# Basic statistics
print("\n📈 STATISTICAL SUMMARY:")
df.describe()

# Product analysis
print("🏷️ PRODUCT ANALYSIS")
print("=" * 40)

unique_products = df['Product'].unique()
print(f"Unique products: {unique_products}")
print(f"Number of products: {len(unique_products)}")

# Sales summary by product
product_summary = df.groupby('Product').agg({
    'Amount': ['count', 'sum', 'mean', 'min', 'max', 'std']
}).round(2)
product_summary.columns = ['Transactions', 'Total_Sales', 'Avg_Sales', 'Min_Sales', 'Max_Sales', 'Std_Dev']

print("\n📊 SALES SUMMARY BY PRODUCT:")
product_summary

# Data filtering examples
print("🔍 DATA FILTERING EXAMPLES")
print("=" * 40)

# High value sales (>180)
high_value_sales = df[df['Amount'] > 180]
print(f"High value sales (>$180): {len(high_value_sales)} transactions")
print(f"Percentage of total: {len(high_value_sales)/len(df)*100:.1f}%")

# Top 10 sales by amount
print("\n🏆 TOP 10 SALES BY AMOUNT:")
top_sales = df.nlargest(10, 'Amount')
top_sales[['Product', 'Amount', 'Date']]

# Calculate data for bar charts
product_sales = df.groupby('Product')['Amount'].sum().sort_values(ascending=False)
avg_sales = df.groupby('Product')['Amount'].mean().sort_values(ascending=False)

# Create bar charts
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

# Bar chart 1: Total Sales by Product
colors1 = ['#FF6B6B', '#4ECDC4', '#45B7D1']
bars1 = ax1.bar(product_sales.index, product_sales.values, color=colors1, alpha=0.8)
ax1.set_title('Total Sales by Product', fontsize=14, fontweight='bold', pad=20)
ax1.set_xlabel('Product', fontsize=12)
ax1.set_ylabel('Total Sales Amount ($)', fontsize=12)
ax1.grid(axis='y', alpha=0.3)

# Add value labels on bars
for bar in bars1:
    height = bar.get_height()
    ax1.text(bar.get_x() + bar.get_width()/2., height + 50,
            f'${height:,.0f}', ha='center', va='bottom', fontweight='bold')

# Bar chart 2: Average Sales by Product
colors2 = ['#96CEB4', '#FFEAA7', '#DDA0DD']
bars2 = ax2.bar(avg_sales.index, avg_sales.values, color=colors2, alpha=0.8)
ax2.set_title('Average Sales by Product', fontsize=14, fontweight='bold', pad=20)
ax2.set_xlabel('Product', fontsize=12)
ax2.set_ylabel('Average Sales Amount ($)', fontsize=12)
ax2.grid(axis='y', alpha=0.3)

# Add value labels on bars
for bar in bars2:
    height = bar.get_height()
    ax2.text(bar.get_x() + bar.get_width()/2., height + 2,
            f'${height:.1f}', ha='center', va='bottom', fontweight='bold')

plt.tight_layout()
plt.savefig('bar_charts.png', dpi=300, bbox_inches='tight')
plt.show()

print("📊 BAR CHART INSIGHTS:")
print(f"• Product C leads in total sales: ${product_sales.iloc[0]:,.0f}")
print(f"• Product C also has highest average sale: ${avg_sales.iloc[0]:.1f}")
print(f"• Revenue gap between top and bottom: ${product_sales.iloc[0] - product_sales.iloc[-1]:,.0f}")

# Prepare data for time series analysis
df['Month'] = df['Date'].dt.to_period('M')
monthly_sales = df.groupby(['Month', 'Product'])['Amount'].sum().unstack(fill_value=0)
monthly_total = df.groupby('Month')['Amount'].sum()

# Create line charts
fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))

# Line chart 1: Monthly sales by product
colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']
for i, product in enumerate(monthly_sales.columns):
    ax1.plot(monthly_sales.index.astype(str), monthly_sales[product], 
            marker='o', linewidth=2.5, markersize=6, label=product, color=colors[i])

ax1.set_title('Monthly Sales Trends by Product', fontsize=14, fontweight='bold', pad=20)
ax1.set_xlabel('Month', fontsize=12)
ax1.set_ylabel('Sales Amount ($)', fontsize=12)
ax1.legend(title='Product', title_fontsize=12, fontsize=10)
ax1.grid(True, alpha=0.3)
ax1.tick_params(axis='x', rotation=45)

# Line chart 2: Total monthly sales trend
ax2.plot(monthly_total.index.astype(str), monthly_total.values, 
        marker='s', linewidth=3, markersize=8, color='#E74C3C', alpha=0.8)
ax2.fill_between(monthly_total.index.astype(str), monthly_total.values, 
                alpha=0.3, color='#E74C3C')

# Add annotations for highest and lowest points
max_idx = monthly_total.idxmax()
min_idx = monthly_total.idxmin()
ax2.annotate(f'Peak: ${monthly_total.max():,.0f}', 
            xy=(str(max_idx), monthly_total.max()), 
            xytext=(10, 10), textcoords='offset points',
            bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7),
            arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))

ax2.set_title('Total Monthly Sales Trend', fontsize=14, fontweight='bold', pad=20)
ax2.set_xlabel('Month', fontsize=12)
ax2.set_ylabel('Total Sales Amount ($)', fontsize=12)
ax2.grid(True, alpha=0.3)
ax2.tick_params(axis='x', rotation=45)

plt.tight_layout()
plt.savefig('line_charts.png', dpi=300, bbox_inches='tight')
plt.show()

print("📈 LINE CHART INSIGHTS:")
print(f"• Peak sales month: {max_idx} with ${monthly_total.max():,.0f}")
print(f"• Lowest sales month: {min_idx} with ${monthly_total.min():,.0f}")
print(f"• Monthly sales range: ${monthly_total.max() - monthly_total.min():,.0f}")
print(f"• Product C consistently outperforms others")

# Prepare data for scatter plots
df['DayOfYear'] = df['Date'].dt.dayofyear
df['DateNum'] = (df['Date'] - df['Date'].min()).dt.days

# Create scatter plots
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))

# Scatter plot 1: Sales Amount vs Day of Year (colored by product)
products = df['Product'].unique()
colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']

for i, product in enumerate(products):
    product_data = df[df['Product'] == product]
    ax1.scatter(product_data['DayOfYear'], product_data['Amount'], 
               c=colors[i], label=product, alpha=0.7, s=50)

# Add trend line
z = np.polyfit(df['DayOfYear'], df['Amount'], 1)
p = np.poly1d(z)
ax1.plot(df['DayOfYear'], p(df['DayOfYear']), "r--", alpha=0.8, linewidth=2, label='Trend')

ax1.set_title('Sales Amount vs Day of Year by Product', fontsize=14, fontweight='bold', pad=20)
ax1.set_xlabel('Day of Year', fontsize=12)
ax1.set_ylabel('Sales Amount ($)', fontsize=12)
ax1.legend(title='Product', title_fontsize=12)
ax1.grid(True, alpha=0.3)

# Scatter plot 2: Bubble chart (size based on amount)
for i, product in enumerate(products):
    product_data = df[df['Product'] == product]
    ax2.scatter(product_data['DateNum'], product_data['Amount'], 
               c=colors[i], label=product, alpha=0.6, 
               s=product_data['Amount']/2)  # Size based on amount

ax2.set_title('Sales Bubble Chart (Size = Amount)', fontsize=14, fontweight='bold', pad=20)
ax2.set_xlabel('Days from Start', fontsize=12)
ax2.set_ylabel('Sales Amount ($)', fontsize=12)
ax2.legend(title='Product', title_fontsize=12)
ax2.grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig('scatter_plots.png', dpi=300, bbox_inches='tight')
plt.show()

# Calculate correlation
correlation = df['DayOfYear'].corr(df['Amount'])
print("🔍 SCATTER PLOT INSIGHTS:")
print(f"• Correlation between Day of Year and Sales: {correlation:.3f}")
print(f"• {'Strong' if abs(correlation) > 0.7 else 'Moderate' if abs(correlation) > 0.3 else 'Weak'} correlation detected")
print(f"• Product C shows highest values (larger bubbles in chart 2)")
print(f"• Sales appear to be relatively consistent throughout the year")

# Create comprehensive dashboard
fig = plt.figure(figsize=(20, 12))
custom_colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD']

# 1. Heatmap of sales by product and month
ax1 = plt.subplot(2, 3, 1)
monthly_product_sales = df.groupby([df['Date'].dt.month, 'Product'])['Amount'].sum().unstack(fill_value=0)
sns.heatmap(monthly_product_sales, annot=True, fmt='.0f', cmap='YlOrRd', 
           cbar_kws={'label': 'Sales Amount'}, ax=ax1)
ax1.set_title('Sales Heatmap by Month and Product', fontsize=12, fontweight='bold')
ax1.set_xlabel('Product')
ax1.set_ylabel('Month')

# 2. Box plot of sales distribution by product
ax2 = plt.subplot(2, 3, 2)
box_data = [df[df['Product'] == product]['Amount'].values for product in df['Product'].unique()]
box_plot = ax2.boxplot(box_data, labels=df['Product'].unique(), patch_artist=True)

# Customize box plot colors
for patch, color in zip(box_plot['boxes'], custom_colors):
    patch.set_facecolor(color)
    patch.set_alpha(0.7)

ax2.set_title('Sales Distribution by Product', fontsize=12, fontweight='bold')
ax2.set_xlabel('Product')
ax2.set_ylabel('Sales Amount ($)')
ax2.grid(axis='y', alpha=0.3)

# 3. Pie chart of total sales by product
ax3 = plt.subplot(2, 3, 3)
product_totals = df.groupby('Product')['Amount'].sum()
wedges, texts, autotexts = ax3.pie(product_totals.values, labels=product_totals.index,
                                  autopct='%1.1f%%', startangle=90, colors=custom_colors[:3])
ax3.set_title('Sales Share by Product', fontsize=12, fontweight='bold')

# Enhance pie chart text
for autotext in autotexts:
    autotext.set_color('white')
    autotext.set_fontweight('bold')

plt.tight_layout()
plt.savefig('advanced_dashboard_part1.png', dpi=300, bbox_inches='tight')
plt.show()

print("🎨 ADVANCED DASHBOARD INSIGHTS:")
print(f"• Heatmap shows Product C dominates across all months")
print(f"• Box plot reveals Product C has highest median and range")
print(f"• Pie chart confirms Product C holds {product_totals['Product C']/product_totals.sum()*100:.1f}% market share")

# Create additional advanced visualizations
fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

# 1. Histogram with statistical lines
ax1.hist(df['Amount'], bins=20, color='#45B7D1', alpha=0.7, edgecolor='black')
ax1.axvline(df['Amount'].mean(), color='red', linestyle='--', linewidth=2, 
           label=f'Mean: ${df["Amount"].mean():.1f}')
ax1.axvline(df['Amount'].median(), color='green', linestyle='--', linewidth=2,
           label=f'Median: ${df["Amount"].median():.1f}')
ax1.set_title('Distribution of Sales Amounts', fontsize=12, fontweight='bold')
ax1.set_xlabel('Sales Amount ($)')
ax1.set_ylabel('Frequency')
ax1.legend()
ax1.grid(axis='y', alpha=0.3)

# 2. Weekly sales trend
df['Week'] = df['Date'].dt.isocalendar().week
weekly_sales = df.groupby('Week')['Amount'].sum()
ax2.plot(weekly_sales.index, weekly_sales.values, marker='o', linewidth=2, 
        markersize=4, color='#E74C3C')
ax2.fill_between(weekly_sales.index, weekly_sales.values, alpha=0.3, color='#E74C3C')
ax2.set_title('Weekly Sales Trend', fontsize=12, fontweight='bold')
ax2.set_xlabel('Week Number')
ax2.set_ylabel('Total Sales ($)')
ax2.grid(True, alpha=0.3)

# 3. Product performance with error bars
product_stats = df.groupby('Product')['Amount'].agg(['mean', 'std']).reset_index()
x_pos = range(len(product_stats))
bars = ax3.bar(x_pos, product_stats['mean'], yerr=product_stats['std'], 
              capsize=5, color=custom_colors[:3], alpha=0.8, 
              error_kw={'elinewidth': 2, 'capthick': 2})
ax3.set_title('Average Sales with Standard Deviation', fontsize=12, fontweight='bold')
ax3.set_xlabel('Product')
ax3.set_ylabel('Average Sales Amount ($)')
ax3.set_xticks(x_pos)
ax3.set_xticklabels(product_stats['Product'])
ax3.grid(axis='y', alpha=0.3)

# 4. Cumulative sales over time
df_sorted = df.sort_values('Date')
df_sorted['Cumulative_Sales'] = df_sorted['Amount'].cumsum()
ax4.plot(df_sorted['Date'], df_sorted['Cumulative_Sales'], linewidth=2, color='#9B59B6')
ax4.fill_between(df_sorted['Date'], df_sorted['Cumulative_Sales'], alpha=0.3, color='#9B59B6')
ax4.set_title('Cumulative Sales Over Time', fontsize=12, fontweight='bold')
ax4.set_xlabel('Date')
ax4.set_ylabel('Cumulative Sales ($)')
ax4.tick_params(axis='x', rotation=45)
ax4.grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig('advanced_dashboard_part2.png', dpi=300, bbox_inches='tight')
plt.show()

print("📊 ADDITIONAL INSIGHTS:")
print(f"• Sales distribution is roughly normal with slight right skew")
print(f"• Weekly pattern shows consistent performance")
print(f"• Product C has highest variability (largest error bars)")
print(f"• Cumulative sales show steady growth: ${df_sorted['Cumulative_Sales'].iloc[-1]:,.0f} total")

# Calculate key business metrics
total_sales = df['Amount'].sum()
avg_daily_sales = df['Amount'].mean()
best_product = df.groupby('Product')['Amount'].sum().idxmax()
worst_product = df.groupby('Product')['Amount'].sum().idxmin()
date_range = (df['Date'].max() - df['Date'].min()).days
volatility = df['Amount'].std()

print("💡 KEY BUSINESS METRICS")
print("=" * 40)
print(f"💰 Total Sales Revenue: ${total_sales:,.2f}")
print(f"📈 Average Daily Sales: ${avg_daily_sales:.2f}")
print(f"📅 Analysis Period: {date_range} days")
print(f"🏆 Best Performing Product: {best_product}")
print(f"📉 Lowest Performing Product: {worst_product}")
print(f"📊 Sales Volatility: ${volatility:.2f}")

# Create business insights visualization
fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
fig.suptitle('Business Intelligence Dashboard', fontsize=16, fontweight='bold', y=0.98)

# 1. KPI Summary (text-based)
ax1.axis('off')
kpi_text = f"""
📊 KEY PERFORMANCE INDICATORS

💰 Total Sales Revenue: ${total_sales:,.2f}
📈 Average Daily Sales: ${avg_daily_sales:.2f}
📅 Analysis Period: {date_range} days
🏆 Best Performing Product: {best_product}
📉 Lowest Performing Product: {worst_product}

🎯 KEY INSIGHTS:
• {best_product} generates the highest revenue
• Sales show consistent patterns across products
• Average transaction value varies by product type
• Opportunity to boost {worst_product} performance
"""
ax1.text(0.05, 0.95, kpi_text, transform=ax1.transAxes, fontsize=11,
        verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))

# 2. Monthly trend with key annotations
monthly_trend = df.groupby(df['Date'].dt.month)['Amount'].sum()
ax2.plot(monthly_trend.index, monthly_trend.values, marker='o', linewidth=3, markersize=8)
ax2.fill_between(monthly_trend.index, monthly_trend.values, alpha=0.3)

# Add annotations for key insights
peak_month = monthly_trend.idxmax()
low_month = monthly_trend.idxmin()

ax2.annotate(f'Peak Month: {peak_month}\n${monthly_trend.max():,.0f}', 
            xy=(peak_month, monthly_trend.max()), 
            xytext=(peak_month+0.5, monthly_trend.max()+500),
            arrowprops=dict(arrowstyle='->', color='red', lw=2),
            bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.8),
            fontsize=10, fontweight='bold')

ax2.set_title('Monthly Sales Trend with Key Insights', fontsize=14, fontweight='bold')
ax2.set_xlabel('Month')
ax2.set_ylabel('Total Sales ($)')
ax2.grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig('business_insights.png', dpi=300, bbox_inches='tight')
plt.show()

print("💡 BUSINESS INSIGHTS SUMMARY:")
print(f"• Peak performance in month {peak_month} suggests seasonal opportunity")
print(f"• Revenue gap of ${monthly_trend.max() - monthly_trend.min():,.0f} between best and worst months")
print(f"• {best_product} could be focus for marketing and inventory")
print(f"• Consider strategies to improve {worst_product} performance")

# Create executive summary visualization
fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 10))
fig.suptitle('Sales Data Analysis - Executive Summary', fontsize=16, fontweight='bold', y=0.98)

# 1. Clean bar chart for total sales
product_sales = df.groupby('Product')['Amount'].sum().sort_values(ascending=False)
bars1 = ax1.bar(product_sales.index, product_sales.values, 
               color=['#2E86AB', '#A23B72', '#F18F01'], alpha=0.8)
ax1.set_title('Total Revenue by Product', fontweight='bold')
ax1.set_ylabel('Revenue ($)')
for bar in bars1:
    height = bar.get_height()
    ax1.text(bar.get_x() + bar.get_width()/2., height + 100,
            f'${height:,.0f}', ha='center', va='bottom', fontweight='bold')

# 2. Monthly trend
monthly_sales = df.groupby(df['Date'].dt.month)['Amount'].sum()
ax2.plot(monthly_sales.index, monthly_sales.values, marker='o', 
        linewidth=3, markersize=8, color='#2E86AB')
ax2.fill_between(monthly_sales.index, monthly_sales.values, alpha=0.3, color='#2E86AB')
ax2.set_title('Monthly Sales Trend', fontweight='bold')
ax2.set_xlabel('Month')
ax2.set_ylabel('Total Sales ($)')
ax2.grid(True, alpha=0.3)

# 3. Sales distribution
ax3.hist(df['Amount'], bins=15, color='#A23B72', alpha=0.7, edgecolor='black')
ax3.axvline(df['Amount'].mean(), color='red', linestyle='--', linewidth=2,
           label=f'Mean: ${df["Amount"].mean():.0f}')
ax3.set_title('Sales Amount Distribution', fontweight='bold')
ax3.set_xlabel('Sales Amount ($)')
ax3.set_ylabel('Frequency')
ax3.legend()

# 4. Key metrics summary
ax4.axis('off')
metrics_text = f"""
📊 KEY METRICS SUMMARY

💰 Total Revenue: ${df['Amount'].sum():,.0f}
📈 Average Sale: ${df['Amount'].mean():.0f}
🏆 Top Product: {df.groupby('Product')['Amount'].sum().idxmax()}
📅 Period: {df['Date'].min().strftime('%Y-%m-%d')} to {df['Date'].max().strftime('%Y-%m-%d')}
📊 Total Transactions: {len(df):,}

🎯 RECOMMENDATIONS:
• Focus marketing on top-performing products
• Investigate seasonal trends for planning
• Optimize inventory based on sales patterns
• Consider promotional strategies for underperforming products
"""
ax4.text(0.05, 0.95, metrics_text, transform=ax4.transAxes, fontsize=11,
        verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8))

plt.tight_layout()
plt.savefig('executive_summary.png', dpi=300, bbox_inches='tight')
plt.savefig('executive_summary.pdf', dpi=300, bbox_inches='tight')  # PDF for presentations
plt.show()

print("✅ Executive summary created and saved as:")
print("   - executive_summary.png (for web/email)")
print("   - executive_summary.pdf (for presentations)")

# Create and export summary reports
summary_data = {
    'Metric': ['Total Sales', 'Average Daily Sales', 'Number of Products', 
              'Total Transactions', 'Analysis Period (days)', 'Best Product', 'Sales Volatility'],
    'Value': [f"${df['Amount'].sum():,.2f}",
             f"${df['Amount'].mean():.2f}",
             f"{df['Product'].nunique()}",
             f"{len(df)}",
             f"{(df['Date'].max() - df['Date'].min()).days}",
             f"{df.groupby('Product')['Amount'].sum().idxmax()}",
             f"${df['Amount'].std():.2f}"]
}

summary_df = pd.DataFrame(summary_data)
summary_df.to_csv('sales_analysis_summary.csv', index=False)
print("✅ Summary report exported to 'sales_analysis_summary.csv'")

# Export detailed product analysis
detailed_analysis = df.groupby('Product').agg({
    'Amount': ['count', 'sum', 'mean', 'min', 'max', 'std']
}).round(2)
detailed_analysis.columns = ['Transactions', 'Total_Sales', 'Avg_Sales', 
                           'Min_Sales', 'Max_Sales', 'Std_Dev']
detailed_analysis.to_csv('detailed_product_analysis.csv')
print("✅ Detailed analysis exported to 'detailed_product_analysis.csv'")

# Display the summary
print("\n📊 ANALYSIS SUMMARY:")
summary_df

# Display detailed product analysis
print("📈 DETAILED PRODUCT ANALYSIS:")
detailed_analysis

# List all generated files
generated_files = [
    'bar_charts.png',
    'line_charts.png', 
    'scatter_plots.png',
    'advanced_dashboard_part1.png',
    'advanced_dashboard_part2.png',
    'business_insights.png',
    'executive_summary.png',
    'executive_summary.pdf',
    'sales_analysis_summary.csv',
    'detailed_product_analysis.csv'
]

print("📁 GENERATED FILES SUMMARY")
print("=" * 40)
print(f"Total files created: {len(generated_files)}")
print("\nVisualization Files:")
for i, file in enumerate([f for f in generated_files if f.endswith('.png')], 1):
    print(f"   {i}. {file}")

print("\nPresentation Files:")
for i, file in enumerate([f for f in generated_files if f.endswith('.pdf')], 1):
    print(f"   {i}. {file}")

print("\nData Export Files:")
for i, file in enumerate([f for f in generated_files if f.endswith('.csv')], 1):
    print(f"   {i}. {file}")

print("\n🎉 ANALYSIS COMPLETE!")
print("All activities have been successfully completed:")
print("✅ Activity 3: Data exploration and filtering")
print("✅ Activity 4: Bar charts, line charts, and scatter plots")
print("✅ Activity 5: Customized visualizations with colors and labels")
print("✅ Activity 6: Business insights with meaningful annotations")
print("✅ Activity 7: Exported visualizations and reports")